# -*- coding: utf-8 -*-
"""
Optuna集成Hook - 集成到MMEngine训练流程中
Created by: Moss
Date: 2025-08-23
"""

import logging
from typing import Optional, Union, Dict, Any
from mmengine.hooks import Hook
from mmengine.runner import Runner
import optuna
from optuna.trial import Trial

logger = logging.getLogger(__name__)


class OptunaHook(Hook):
    """
    Optuna集成Hook
    
    功能：
    1. 实时监控训练过程
    2. 支持early pruning机制
    3. 记录中间结果用于可视化
    """
    
    def __init__(self,
                 trial: Optional[Trial] = None,
                 monitor_metric: str = 'val/acc',
                 pruning_enabled: bool = True,
                 min_epochs: int = 10,
                 patience: int = 5):
        """
        初始化OptunaHook
        
        Args:
            trial: Optuna trial对象
            monitor_metric: 监控的指标名称
            pruning_enabled: 是否启用剪枝
            min_epochs: 最小训练轮数（在此之前不进行剪枝）
            patience: 剪枝的耐心值
        """
        super().__init__()
        self.trial = trial
        self.monitor_metric = monitor_metric
        self.pruning_enabled = pruning_enabled
        self.min_epochs = min_epochs
        self.patience = patience
        
        # 记录历史指标
        self.metric_history = []
        self.best_metric = None
        self.no_improve_count = 0
        
        logger.info(f"OptunaHook initialized with monitor_metric: {monitor_metric}")
    
    def after_val_epoch(self, runner: Runner) -> None:
        """
        验证后回调函数
        
        Args:
            runner: MMEngine runner对象
        """
        if not self.trial or not self.pruning_enabled:
            return
        
        current_epoch = runner.epoch
        
        # 获取当前指标值
        current_metric = self._get_metric_value(runner)
        
        if current_metric is None:
            logger.warning(f"Could not find metric: {self.monitor_metric}")
            return
        
        # 记录指标历史
        self.metric_history.append(current_metric)
        
        # 报告中间值给optuna
        self.trial.report(current_metric, current_epoch)
        
        # 更新最佳指标
        if self.best_metric is None or current_metric > self.best_metric:
            self.best_metric = current_metric
            self.no_improve_count = 0
        else:
            self.no_improve_count += 1
        
        logger.info(f"Epoch {current_epoch}: {self.monitor_metric} = {current_metric:.4f}")
        
        # 检查是否需要剪枝
        if self.should_prune(current_epoch):
            logger.info(f"Trial pruned at epoch {current_epoch}")
            raise optuna.TrialPruned()
    
    def should_prune(self, current_epoch: int) -> bool:
        """
        判断是否需要剪枝
        
        Args:
            current_epoch: 当前轮数
            
        Returns:
            是否需要剪枝
        """
        if not self.trial or not self.pruning_enabled:
            return False
        
        # 最小轮数检查
        if current_epoch < self.min_epochs:
            return False
        
        # 使用optuna的剪枝器判断
        if self.trial.should_prune():
            return True
        
        # 基于耐心值的剪枝
        if self.no_improve_count >= self.patience:
            logger.info(f"No improvement for {self.patience} epochs, suggesting pruning")
            return True
        
        return False
    
    def _get_metric_value(self, runner: Runner) -> Optional[float]:
        """
        从runner中获取指标值
        
        Args:
            runner: MMEngine runner对象
            
        Returns:
            指标值，如果找不到则返回None
        """
        try:
            # 尝试从log_buffer中获取指标
            if hasattr(runner, 'log_buffer') and runner.log_buffer:
                log_buffer = runner.log_buffer
                
                # 检查不同可能的指标名称格式
                possible_names = [
                    self.monitor_metric,
                    f"val/{self.monitor_metric}",
                    f"val_{self.monitor_metric}",
                    self.monitor_metric.replace('val/', ''),
                    self.monitor_metric.replace('val_', '')
                ]
                
                for name in possible_names:
                    if name in log_buffer.output:
                        return float(log_buffer.output[name])
            
            # 尝试从message_hub获取
            if hasattr(runner, 'message_hub'):
                message_hub = runner.message_hub
                for name in possible_names:
                    if message_hub.get_info(name) is not None:
                        return float(message_hub.get_info(name))
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting metric value: {str(e)}")
            return None
    
    def before_train(self, runner: Runner) -> None:
        """训练开始前的回调"""
        if self.trial:
            logger.info(f"Starting training for trial {self.trial.number}")
    
    def after_train(self, runner: Runner) -> None:
        """训练结束后的回调"""
        if self.trial:
            final_metric = self.best_metric if self.best_metric is not None else 0.0
            logger.info(f"Training completed for trial {self.trial.number}")
            logger.info(f"Best {self.monitor_metric}: {final_metric:.4f}")
    
    def before_epoch(self, runner: Runner) -> None:
        """每个epoch开始前的回调"""
        pass
    
    def after_epoch(self, runner: Runner) -> None:
        """每个epoch结束后的回调"""
        pass


class OptunaMetricTracker:
    """
    Optuna指标跟踪器
    
    用于在没有trial对象时也能跟踪指标
    """
    
    def __init__(self, monitor_metric: str = 'val/acc'):
        """
        初始化指标跟踪器
        
        Args:
            monitor_metric: 监控的指标名称
        """
        self.monitor_metric = monitor_metric
        self.metric_history = []
        self.best_metric = None
        self.best_epoch = -1
    
    def update(self, metric_value: float, epoch: int) -> None:
        """
        更新指标
        
        Args:
            metric_value: 指标值
            epoch: 轮数
        """
        self.metric_history.append((epoch, metric_value))
        
        if self.best_metric is None or metric_value > self.best_metric:
            self.best_metric = metric_value
            self.best_epoch = epoch
    
    def get_best_metric(self) -> tuple:
        """
        获取最佳指标
        
        Returns:
            (最佳指标值, 最佳轮数)
        """
        return self.best_metric, self.best_epoch
    
    def get_history(self) -> list:
        """
        获取指标历史
        
        Returns:
            指标历史列表
        """
        return self.metric_history.copy()


def create_optuna_hook(trial: Optional[Trial] = None, 
                      monitor_metric: str = 'val/acc',
                      **kwargs) -> OptunaHook:
    """
    创建OptunaHook的便捷函数
    
    Args:
        trial: Optuna trial对象
        monitor_metric: 监控指标
        **kwargs: 其他参数
        
    Returns:
        OptunaHook实例
    """
    return OptunaHook(
        trial=trial,
        monitor_metric=monitor_metric,
        **kwargs
    )
