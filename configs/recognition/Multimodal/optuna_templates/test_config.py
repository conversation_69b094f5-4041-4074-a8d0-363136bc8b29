# Optuna Base Configuration Template
# This template will be extended with hyperparameter-specific configurations

# Import base configuration
_base_ = '../multimodal_poseGCN-rgbR50_fusion.py'

# Base settings that will be overridden by hyperparameter configurations
# The actual hyperparameter values will be appended to this template


# Optuna Hyperparameter Configuration

# Optimizer Configuration
optim_wrapper = dict(
    type='OptimWrapper',
    optimizer=dict(
        type='AdamW',
        lr=0.001,
        betas=(0.9, 0.999),
        weight_decay=0.0001
    ),
    paramwise_cfg=dict(
        custom_keys={
            'image_backbone': dict(lr_mult=0.1),
            'pose_backbone': dict(lr_mult=1.0),
            'fusion_neck': dict(lr_mult=1.0),
            'cls_head': dict(lr_mult=1.2)
        }
    ),
    clip_grad=dict(max_norm=20, norm_type=2)
)

# Model Configuration Updates
model.update(dict(
    fusion_neck=dict(
        type='MultiModalFusionNeck',
        pose_feat_dim=256,
        img_feat_dim=320,
        fusion_dim=512,
        fusion_type='attention',
        dropout=0.5
    )
))

# DataLoader Configuration Updates
train_dataloader.update(dict(batch_size=4))
val_dataloader.update(dict(batch_size=4))

# Learning Rate Scheduler Configuration
param_scheduler = [
    dict(
        type='LinearLR',
        start_factor=0.01,
        by_epoch=True,
        begin=0,
        end=5
    ),
    dict(
        type='CosineAnnealingLR',
        T_max=95,
        eta_min=1e-6,
        by_epoch=True,
        begin=5,
        end=100
    )
]
