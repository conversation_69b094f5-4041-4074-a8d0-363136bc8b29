# Optuna超参数配置指南

## 概述

本文档详细说明如何配置和修改Optuna超参数优化框架中的搜索空间。

## 1. 超参数搜索空间配置位置

### 主要配置文件
- **核心配置**: `tools/optuna_trainer.py` 中的 `suggest_hyperparameters` 方法
- **辅助配置**: `tools/optuna_config_generator.py` 中的 `get_hyperparameter_spaces` 方法

### 配置应用位置
- **配置生成**: `tools/optuna_trainer.py` 中的 `create_config` 方法
- **模板应用**: `tools/optuna_config_generator.py` 中的各个配置生成方法

## 2. 当前支持的超参数

### 高优先级超参数（对性能影响最大）

#### 学习率相关
```python
# 基础学习率 (对数尺度)
'base_lr': trial.suggest_float('base_lr', 1e-5, 1e-2, log=True)

# 图像骨干网络学习率倍数
'image_backbone_lr_mult': trial.suggest_float('image_backbone_lr_mult', 0.05, 0.2)

# 融合模块学习率倍数 (新增)
'fusion_neck_lr_mult': trial.suggest_float('fusion_neck_lr_mult', 0.5, 2.0)

# 分类头学习率倍数
'cls_head_lr_mult': trial.suggest_float('cls_head_lr_mult', 0.8, 2.0)
```

#### 正则化参数
```python
# 融合模块dropout率
'fusion_dropout': trial.suggest_float('fusion_dropout', 0.3, 0.7)

# 权重衰减 (对数尺度)
'weight_decay': trial.suggest_float('weight_decay', 1e-5, 1e-3, log=True)
```

#### 训练策略
```python
# 批次大小 (分类选择)
'batch_size': trial.suggest_categorical('batch_size', [2, 4, 6, 8])
```

### 中优先级超参数

#### 模型结构
```python
# 融合特征维度 (分类选择)
'fusion_dim': trial.suggest_categorical('fusion_dim', [256, 384, 512, 768, 1024])

# 预热轮数
'warmup_epochs': trial.suggest_int('warmup_epochs', 3, 10)
```

## 3. 如何修改超参数搜索空间

### 3.1 修改现有参数的搜索范围

**位置**: `tools/optuna_trainer.py` 第58-75行

**示例**: 修改学习率倍数的搜索范围
```python
# 原始配置
hyperparams['cls_head_lr_mult'] = trial.suggest_float('cls_head_lr_mult', 0.8, 2.0)

# 修改为更大的搜索范围
hyperparams['cls_head_lr_mult'] = trial.suggest_float('cls_head_lr_mult', 0.5, 3.0)
```

### 3.2 添加新的超参数

#### 步骤1: 在suggest_hyperparameters方法中添加参数
**文件**: `tools/optuna_trainer.py`
**位置**: `suggest_hyperparameters` 方法内

```python
# 添加新参数示例
hyperparams['new_param'] = trial.suggest_float('new_param', 0.1, 1.0)
```

#### 步骤2: 在create_config方法中应用参数
**文件**: `tools/optuna_trainer.py`
**位置**: `create_config` 方法内的相应配置部分

```python
# 在优化器配置中应用
custom_keys={{
    'image_backbone': dict(lr_mult={hyperparams['image_backbone_lr_mult']}),
    'pose_backbone': dict(lr_mult=1.0),
    'fusion_neck': dict(lr_mult={hyperparams['fusion_neck_lr_mult']}),
    'cls_head': dict(lr_mult={hyperparams['cls_head_lr_mult']}),
    'new_module': dict(lr_mult={hyperparams['new_param']})  # 新增参数应用
}}
```

#### 步骤3: 更新参数空间定义（可选）
**文件**: `tools/optuna_config_generator.py`
**位置**: `get_hyperparameter_spaces` 方法内

```python
'new_param': {
    'type': 'float',
    'low': 0.1,
    'high': 1.0,
    'description': '新参数的描述'
}
```

## 4. 具体配置示例

### 4.1 学习率倍数配置示例

根据您选中的代码 `'cls_head': dict(lr_mult=1.0)`，这是在原始配置文件中的默认设置。

**当前优化配置**:
```python
# 在optuna_trainer.py中
hyperparams['cls_head_lr_mult'] = trial.suggest_float('cls_head_lr_mult', 0.8, 2.0)

# 应用到配置中
'cls_head': dict(lr_mult={hyperparams['cls_head_lr_mult']})
```

**如果要修改搜索范围**:
```python
# 扩大搜索范围
hyperparams['cls_head_lr_mult'] = trial.suggest_float('cls_head_lr_mult', 0.5, 3.0)

# 或使用分类选择
hyperparams['cls_head_lr_mult'] = trial.suggest_categorical('cls_head_lr_mult', [0.5, 1.0, 1.5, 2.0, 2.5])
```

### 4.2 添加新模块的学习率倍数

**步骤1**: 在 `suggest_hyperparameters` 中添加
```python
hyperparams['pose_backbone_lr_mult'] = trial.suggest_float('pose_backbone_lr_mult', 0.5, 2.0)
```

**步骤2**: 在配置生成中应用
```python
custom_keys={{
    'image_backbone': dict(lr_mult={hyperparams['image_backbone_lr_mult']}),
    'pose_backbone': dict(lr_mult={hyperparams['pose_backbone_lr_mult']}),  # 修改这里
    'fusion_neck': dict(lr_mult={hyperparams['fusion_neck_lr_mult']}),
    'cls_head': dict(lr_mult={hyperparams['cls_head_lr_mult']})
}}
```

## 5. 参数类型说明

### 5.1 连续参数 (suggest_float)
```python
# 普通范围
trial.suggest_float('param_name', low, high)

# 对数尺度 (适合学习率等跨度大的参数)
trial.suggest_float('param_name', low, high, log=True)
```

### 5.2 整数参数 (suggest_int)
```python
trial.suggest_int('param_name', low, high)
```

### 5.3 分类参数 (suggest_categorical)
```python
trial.suggest_categorical('param_name', [choice1, choice2, choice3])
```

## 6. 测试新配置

### 6.1 验证配置正确性
```bash
# 运行配置验证测试
python tools/test_optuna_mini.py --mode validate
```

### 6.2 小规模测试
```bash
# 运行少量试验测试新配置
python tools/optuna_trainer.py --n-trials 3
```

### 6.3 查看参数重要性
```bash
# 分析参数重要性
python tools/optuna_analyzer.py --study-name your_study --action importance
```

## 7. 最佳实践

### 7.1 参数搜索范围设置
- **学习率**: 使用对数尺度，范围通常为 [1e-5, 1e-2]
- **学习率倍数**: 通常在 [0.1, 2.0] 范围内
- **Dropout**: 通常在 [0.1, 0.7] 范围内
- **批次大小**: 根据GPU内存限制选择合适的选项

### 7.2 优化策略
1. **先优化高影响参数**: 学习率、正则化参数
2. **再优化结构参数**: 模型维度、层数等
3. **最后优化训练策略**: 批次大小、调度器参数

### 7.3 实验管理
- 使用有意义的study名称
- 定期保存和分析结果
- 根据参数重要性调整搜索空间

## 8. 常见问题

### Q1: 如何知道哪些参数可以优化？
A1: 查看原始配置文件 `configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py`，任何数值型参数都可以加入优化。

### Q2: 参数搜索范围如何确定？
A2: 
- 参考论文中的典型值范围
- 从原始配置值的0.1倍到10倍开始
- 根据初步实验结果调整

### Q3: 如何处理参数之间的依赖关系？
A3: 在 `suggest_hyperparameters` 方法中添加条件逻辑：
```python
if hyperparams['use_feature_a']:
    hyperparams['feature_a_param'] = trial.suggest_float('feature_a_param', 0.1, 1.0)
else:
    hyperparams['feature_a_param'] = 0.0
```

---

**更新时间**: 2025-08-23  
**版本**: v1.0
