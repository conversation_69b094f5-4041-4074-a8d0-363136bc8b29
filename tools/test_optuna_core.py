#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Optuna核心功能测试脚本
Created by: Moss
Date: 2025-08-23
"""

import os
import sys
import tempfile
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.optuna_trainer import OptunaTrainer
import optuna

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_optuna_trainer_init():
    """测试OptunaTrainer初始化"""
    logger.info("Testing OptunaTrainer initialization...")
    
    try:
        base_config = "configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py"
        
        if not os.path.exists(base_config):
            logger.error(f"❌ Base config not found: {base_config}")
            return False
        
        trainer = OptunaTrainer(
            base_config_path=base_config,
            study_name="test_study",
            work_dir_base="./work_dirs/test_optuna"
        )
        
        logger.info("✅ OptunaTrainer initialization test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ OptunaTrainer initialization test failed: {str(e)}")
        return False


def test_hyperparameter_suggestion():
    """测试超参数建议功能"""
    logger.info("Testing hyperparameter suggestion...")
    
    try:
        # 创建一个临时的study和trial
        study = optuna.create_study(direction='minimize')
        trial = study.ask()
        
        base_config = "configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py"
        trainer = OptunaTrainer(base_config_path=base_config)
        
        # 测试超参数建议
        hyperparams = trainer.suggest_hyperparameters(trial)
        
        # 验证超参数
        required_params = [
            'base_lr', 'image_backbone_lr_mult', 'cls_head_lr_mult',
            'fusion_dropout', 'weight_decay', 'batch_size',
            'fusion_dim', 'warmup_epochs'
        ]
        
        for param in required_params:
            if param not in hyperparams:
                logger.error(f"❌ Missing hyperparameter: {param}")
                return False
        
        logger.info("✅ Hyperparameter suggestion test passed")
        logger.info(f"Sample hyperparameters: {hyperparams}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Hyperparameter suggestion test failed: {str(e)}")
        return False


def test_config_creation():
    """测试配置文件创建"""
    logger.info("Testing config creation...")
    
    try:
        base_config = "configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py"
        trainer = OptunaTrainer(base_config_path=base_config)
        
        # 测试超参数
        test_hyperparams = {
            'base_lr': 0.001,
            'image_backbone_lr_mult': 0.1,
            'cls_head_lr_mult': 1.2,
            'fusion_dropout': 0.5,
            'weight_decay': 0.0001,
            'batch_size': 4,
            'fusion_dim': 512,
            'warmup_epochs': 5
        }
        
        # 创建配置文件
        config_path = trainer.create_config(test_hyperparams, trial_id=999)
        
        # 验证文件是否创建
        if not os.path.exists(config_path):
            logger.error(f"❌ Config file was not created: {config_path}")
            return False
        
        # 验证配置内容
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键配置是否存在
        required_configs = [
            'optim_wrapper',
            'model.update',
            'train_dataloader.update',
            'param_scheduler',
            'custom_hooks'
        ]
        
        for config in required_configs:
            if config not in content:
                logger.error(f"❌ Missing config section: {config}")
                return False
        
        logger.info("✅ Config creation test passed")
        logger.info(f"Config file created: {config_path}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Config creation test failed: {str(e)}")
        return False


def test_hook_import():
    """测试OptunaHook导入"""
    logger.info("Testing OptunaHook import...")
    
    try:
        from mmaction.engine.hooks import OptunaHook, OptunaMetricTracker, create_optuna_hook
        
        # 测试创建Hook
        hook = OptunaHook(monitor_metric='val/acc')
        
        # 测试MetricTracker
        tracker = OptunaMetricTracker(monitor_metric='val/acc')
        tracker.update(0.85, 1)
        tracker.update(0.87, 2)
        
        best_metric, best_epoch = tracker.get_best_metric()
        if best_metric != 0.87 or best_epoch != 2:
            logger.error(f"❌ MetricTracker test failed: {best_metric}, {best_epoch}")
            return False
        
        logger.info("✅ OptunaHook import test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ OptunaHook import test failed: {str(e)}")
        return False


def test_metric_extraction():
    """测试指标提取功能"""
    logger.info("Testing metric extraction...")
    
    try:
        base_config = "configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py"
        trainer = OptunaTrainer(base_config_path=base_config)
        
        # 创建模拟日志内容
        mock_log_content = """
        2025-08-23 17:00:00,000 - INFO - Epoch 1: loss: 1.234, acc: 0.567
        2025-08-23 17:01:00,000 - INFO - Epoch 2: loss: 1.123, acc: 0.678
        2025-08-23 17:02:00,000 - INFO - Epoch 3: loss: 1.012, val/acc: 0.789
        2025-08-23 17:03:00,000 - INFO - Epoch 4: loss: 0.987, val/acc: 0.823
        """
        
        # 测试准确率提取
        best_acc = trainer._extract_best_accuracy(mock_log_content)
        if best_acc != 0.823:
            logger.error(f"❌ Accuracy extraction failed: {best_acc}")
            return False
        
        # 测试损失提取
        best_loss = trainer._extract_best_loss(mock_log_content)
        if best_loss != 0.987:
            logger.error(f"❌ Loss extraction failed: {best_loss}")
            return False
        
        logger.info("✅ Metric extraction test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Metric extraction test failed: {str(e)}")
        return False


def main():
    """运行所有核心功能测试"""
    logger.info("=" * 60)
    logger.info("Optuna核心功能测试")
    logger.info("=" * 60)
    
    tests = [
        ("OptunaTrainer初始化测试", test_optuna_trainer_init),
        ("超参数建议测试", test_hyperparameter_suggestion),
        ("配置文件创建测试", test_config_creation),
        ("OptunaHook导入测试", test_hook_import),
        ("指标提取测试", test_metric_extraction),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 运行测试: {test_name}")
        try:
            if test_func():
                passed += 1
            else:
                logger.error(f"测试失败: {test_name}")
        except Exception as e:
            logger.error(f"测试异常: {test_name} - {str(e)}")
    
    logger.info("\n" + "=" * 60)
    logger.info(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有核心功能测试通过！")
        logger.info("可以继续进行实际的optuna优化测试")
    else:
        logger.error("❌ 部分测试失败，请检查并修复问题")
    
    logger.info("=" * 60)


if __name__ == '__main__':
    main()
