#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Optuna基础功能测试脚本
Created by: Moss
Date: 2025-08-23
"""

import os
import sys
import tempfile
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.optuna_config_generator import ConfigGenerator, create_base_template

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_config_generation():
    """测试配置文件生成功能"""
    logger.info("Testing config generation...")
    
    try:
        # 创建基础模板
        template_path = create_base_template()
        
        # 创建配置生成器
        generator = ConfigGenerator(template_path)
        
        # 测试超参数
        test_hyperparams = {
            'base_lr': 0.001,
            'image_backbone_lr_mult': 0.1,
            'cls_head_lr_mult': 1.2,
            'fusion_dropout': 0.5,
            'weight_decay': 0.0001,
            'batch_size': 4,
            'fusion_dim': 512,
            'warmup_epochs': 5
        }
        
        # 生成测试配置
        output_path = "configs/recognition/Multimodal/optuna_templates/test_config.py"
        generated_path = generator.generate_config(test_hyperparams, output_path)
        
        # 检查文件是否生成
        if os.path.exists(generated_path):
            logger.info("✅ Config generation test passed")
            
            # 显示生成的配置内容
            with open(generated_path, 'r', encoding='utf-8') as f:
                content = f.read()
            logger.info(f"Generated config preview:\n{content[-500:]}")  # 显示最后500字符
            
            return True
        else:
            logger.error("❌ Config file was not generated")
            return False
            
    except Exception as e:
        logger.error(f"❌ Config generation test failed: {str(e)}")
        return False


def test_hyperparameter_spaces():
    """测试超参数空间定义"""
    logger.info("Testing hyperparameter spaces...")
    
    try:
        spaces = ConfigGenerator.get_hyperparameter_spaces()
        
        if not spaces:
            logger.error("❌ No hyperparameter spaces defined")
            return False
        
        logger.info("✅ Hyperparameter spaces test passed")
        logger.info("Available hyperparameters:")
        for name, space in spaces.items():
            logger.info(f"  - {name}: {space.get('description', 'No description')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Hyperparameter spaces test failed: {str(e)}")
        return False


def test_optuna_import():
    """测试optuna导入"""
    logger.info("Testing optuna import...")
    
    try:
        import optuna
        logger.info(f"✅ Optuna import test passed (version: {optuna.__version__})")
        return True
    except ImportError as e:
        logger.error(f"❌ Optuna import test failed: {str(e)}")
        return False


def test_base_config_exists():
    """测试基础配置文件是否存在"""
    logger.info("Testing base config file existence...")
    
    base_config_path = "configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py"
    
    if os.path.exists(base_config_path):
        logger.info("✅ Base config file exists")
        return True
    else:
        logger.error(f"❌ Base config file not found: {base_config_path}")
        return False


def test_directory_structure():
    """测试目录结构"""
    logger.info("Testing directory structure...")
    
    required_dirs = [
        "configs/recognition/Multimodal/optuna_templates",
        "tools",
        "mmaction/engine/hooks"
    ]
    
    all_exist = True
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            logger.info(f"✅ Directory exists: {dir_path}")
        else:
            logger.error(f"❌ Directory missing: {dir_path}")
            all_exist = False
    
    return all_exist


def main():
    """运行所有测试"""
    logger.info("=" * 60)
    logger.info("Optuna基础功能测试")
    logger.info("=" * 60)
    
    tests = [
        ("Optuna导入测试", test_optuna_import),
        ("目录结构测试", test_directory_structure),
        ("基础配置文件测试", test_base_config_exists),
        ("超参数空间测试", test_hyperparameter_spaces),
        ("配置生成测试", test_config_generation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 运行测试: {test_name}")
        try:
            if test_func():
                passed += 1
            else:
                logger.error(f"测试失败: {test_name}")
        except Exception as e:
            logger.error(f"测试异常: {test_name} - {str(e)}")
    
    logger.info("\n" + "=" * 60)
    logger.info(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有基础功能测试通过！")
        logger.info("可以继续进行阶段2的核心功能实现")
    else:
        logger.error("❌ 部分测试失败，请检查并修复问题")
    
    logger.info("=" * 60)


if __name__ == '__main__':
    main()
