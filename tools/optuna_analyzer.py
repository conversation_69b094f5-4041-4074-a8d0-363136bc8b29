#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Optuna结果分析工具
Created by: Moss
Date: 2025-08-23
"""

import os
import sys
import argparse
import logging
from typing import Dict, List, Any, Optional
import optuna
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 设置字体（使用英文，避免中文乱码）
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class OptunaAnalyzer:
    """
    Optuna结果分析器
    
    功能：
    1. 分析优化结果
    2. 生成可视化图表
    3. 提取最佳超参数
    4. 生成优化报告
    """
    
    def __init__(self, storage_url: str = "sqlite:///work_dirs/optuna_studies.db"):
        """
        初始化分析器
        
        Args:
            storage_url: 存储URL
        """
        self.storage_url = storage_url
    
    def load_study(self, study_name: str) -> optuna.Study:
        """加载study"""
        try:
            study = optuna.load_study(
                study_name=study_name,
                storage=self.storage_url
            )
            logger.info(f"✅ 加载study成功: {study_name}")
            return study
        except Exception as e:
            logger.error(f"❌ 加载study失败: {str(e)}")
            raise
    
    def analyze_optimization_history(self, study: optuna.Study, output_dir: str = "./work_dirs/analysis"):
        """分析优化历史"""
        logger.info("📈 分析优化历史")
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建优化历史图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 目标值历史
        trials = [t for t in study.trials if t.value is not None]
        trial_numbers = [t.number for t in trials]
        values = [t.value for t in trials]
        
        ax1.plot(trial_numbers, values, 'b-', alpha=0.7, label='Trial Values')

        # 最佳值历史
        best_values = []
        current_best = float('inf') if study.direction == optuna.study.StudyDirection.MINIMIZE else float('-inf')

        for value in values:
            if study.direction == optuna.study.StudyDirection.MINIMIZE:
                current_best = min(current_best, value)
            else:
                current_best = max(current_best, value)
            best_values.append(current_best)

        ax1.plot(trial_numbers, best_values, 'r-', linewidth=2, label='Best Values')
        ax1.set_xlabel('Trial Number')
        ax1.set_ylabel('Objective Value')
        ax1.set_title('Optimization History')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 目标值分布
        ax2.hist(values, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax2.axvline(study.best_value, color='red', linestyle='--', linewidth=2, label=f'Best: {study.best_value:.4f}')
        ax2.set_xlabel('Objective Value')
        ax2.set_ylabel('Frequency')
        ax2.set_title('Objective Value Distribution')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        history_path = os.path.join(output_dir, 'optimization_history.png')
        plt.savefig(history_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"📊 优化历史图已保存: {history_path}")
    
    def analyze_parameter_importance(self, study: optuna.Study, output_dir: str = "./work_dirs/analysis"):
        """分析参数重要性"""
        logger.info("🔍 分析参数重要性")
        
        os.makedirs(output_dir, exist_ok=True)
        
        try:
            # 计算参数重要性
            importance = optuna.importance.get_param_importances(study)
            
            if not importance:
                logger.warning("⚠️ 无法计算参数重要性（可能试验数量不足）")
                return
            
            # 创建重要性图
            params = list(importance.keys())
            values = list(importance.values())
            
            plt.figure(figsize=(10, 6))
            bars = plt.barh(params, values, color='lightcoral')
            plt.xlabel('Importance')
            plt.title('Hyperparameter Importance Analysis')
            plt.grid(True, alpha=0.3)

            # 添加数值标签
            for bar, value in zip(bars, values):
                plt.text(value + max(values) * 0.01, bar.get_y() + bar.get_height()/2,
                        f'{value:.3f}', va='center')
            
            plt.tight_layout()
            importance_path = os.path.join(output_dir, 'parameter_importance.png')
            plt.savefig(importance_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"📊 参数重要性图已保存: {importance_path}")
            
            # 输出重要性排序
            logger.info("📋 参数重要性排序:")
            for param, imp in sorted(importance.items(), key=lambda x: x[1], reverse=True):
                logger.info(f"  {param}: {imp:.4f}")
                
        except Exception as e:
            logger.warning(f"⚠️ 参数重要性分析失败: {str(e)}")
    
    def analyze_parameter_relationships(self, study: optuna.Study, output_dir: str = "./work_dirs/analysis"):
        """分析参数关系"""
        logger.info("🔗 分析参数关系")
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 获取完成的试验数据
        completed_trials = [t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE]
        
        if len(completed_trials) < 2:
            logger.warning("⚠️ 完成的试验数量不足，无法分析参数关系")
            return
        
        # 创建数据框
        data = []
        for trial in completed_trials:
            row = {'value': trial.value}
            row.update(trial.params)
            data.append(row)
        
        df = pd.DataFrame(data)
        
        # 选择数值型参数
        numeric_params = []
        for col in df.columns:
            if col != 'value' and pd.api.types.is_numeric_dtype(df[col]):
                numeric_params.append(col)
        
        if len(numeric_params) < 2:
            logger.warning("⚠️ 数值型参数不足，无法创建关系图")
            return
        
        # 创建相关性矩阵
        corr_data = df[numeric_params + ['value']].corr()
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(corr_data, annot=True, cmap='coolwarm', center=0,
                   square=True, fmt='.3f')
        plt.title('Parameter Correlation Matrix')
        plt.tight_layout()
        
        correlation_path = os.path.join(output_dir, 'parameter_correlation.png')
        plt.savefig(correlation_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"📊 参数相关性图已保存: {correlation_path}")
    
    def generate_report(self, study_name: str, output_dir: str = "./work_dirs/analysis"):
        """生成完整的分析报告"""
        logger.info("📝 生成分析报告")
        
        os.makedirs(output_dir, exist_ok=True)
        
        study = self.load_study(study_name)
        
        # 基本统计信息
        completed_trials = [t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE]
        failed_trials = [t for t in study.trials if t.state == optuna.trial.TrialState.FAIL]
        pruned_trials = [t for t in study.trials if t.state == optuna.trial.TrialState.PRUNED]
        
        report_content = f"""# Optuna优化分析报告

## 基本信息
- **Study名称**: {study_name}
- **优化方向**: {'最小化' if study.direction == optuna.study.StudyDirection.MINIMIZE else '最大化'}
- **总试验数**: {len(study.trials)}
- **完成试验数**: {len(completed_trials)}
- **失败试验数**: {len(failed_trials)}
- **剪枝试验数**: {len(pruned_trials)}

## 最佳结果
"""
        
        if study.best_trial:
            report_content += f"""- **最佳试验编号**: {study.best_trial.number}
- **最佳目标值**: {study.best_value:.6f}
- **最佳参数**:
"""
            for param, value in study.best_params.items():
                report_content += f"  - {param}: {value}\n"
        else:
            report_content += "- 暂无完成的试验\n"
        
        # 参数统计
        if completed_trials:
            report_content += "\n## 参数统计\n"
            
            # 收集所有参数
            all_params = set()
            for trial in completed_trials:
                all_params.update(trial.params.keys())
            
            for param in sorted(all_params):
                values = [trial.params.get(param) for trial in completed_trials if param in trial.params]
                if values and all(isinstance(v, (int, float)) for v in values):
                    report_content += f"- **{param}**:\n"
                    report_content += f"  - 最小值: {min(values):.6f}\n"
                    report_content += f"  - 最大值: {max(values):.6f}\n"
                    report_content += f"  - 平均值: {sum(values)/len(values):.6f}\n"
        
        # 保存报告
        report_path = os.path.join(output_dir, f'{study_name}_analysis_report.md')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"📄 分析报告已保存: {report_path}")
        
        # 生成可视化图表
        self.analyze_optimization_history(study, output_dir)
        self.analyze_parameter_importance(study, output_dir)
        self.analyze_parameter_relationships(study, output_dir)
        
        logger.info("✅ 完整分析报告生成完成")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Optuna结果分析工具')
    parser.add_argument('--study-name', required=True, help='Study名称')
    parser.add_argument('--storage', 
                       default='sqlite:///work_dirs/optuna_studies.db',
                       help='存储URL')
    parser.add_argument('--output-dir', 
                       default='./work_dirs/analysis',
                       help='输出目录')
    parser.add_argument('--action',
                       choices=['report', 'history', 'importance', 'correlation'],
                       default='report',
                       help='分析类型')
    
    args = parser.parse_args()
    
    logger.info("=" * 60)
    logger.info("Optuna结果分析工具")
    logger.info("=" * 60)
    
    analyzer = OptunaAnalyzer(storage_url=args.storage)
    
    try:
        if args.action == 'report':
            analyzer.generate_report(args.study_name, args.output_dir)
        else:
            study = analyzer.load_study(args.study_name)
            
            if args.action == 'history':
                analyzer.analyze_optimization_history(study, args.output_dir)
            elif args.action == 'importance':
                analyzer.analyze_parameter_importance(study, args.output_dir)
            elif args.action == 'correlation':
                analyzer.analyze_parameter_relationships(study, args.output_dir)
                
    except Exception as e:
        logger.error(f"❌ 分析失败: {str(e)}")
    
    logger.info("=" * 60)


if __name__ == '__main__':
    main()
