#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Optuna训练管理器 - 集成optuna超参数优化到mmaction2训练流程
Created by: Moss
Date: 2025-08-23
"""

import os
import sys
import json
import argparse
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, Any, Optional, Union
import optuna
from optuna.trial import Trial
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OptunaTrainer:
    """
    Optuna训练管理器
    
    功能：
    1. 作为optuna和mmaction2训练流程的桥梁
    2. 动态生成配置文件
    3. 管理训练进程
    4. 收集和返回优化目标值
    """
    
    def __init__(self, 
                 base_config_path: str,
                 study_name: str = "mmaction2_hyperopt",
                 storage_url: Optional[str] = None,
                 work_dir_base: str = "./work_dirs/optuna_trials"):
        """
        初始化Optuna训练管理器
        
        Args:
            base_config_path: 基础配置文件路径
            study_name: optuna study名称
            storage_url: optuna存储URL，None表示使用内存存储
            work_dir_base: 试验工作目录基础路径
        """
        self.base_config_path = base_config_path
        self.study_name = study_name
        self.storage_url = storage_url
        self.work_dir_base = work_dir_base
        
        # 确保工作目录存在
        os.makedirs(work_dir_base, exist_ok=True)
        
        # 验证基础配置文件存在
        if not os.path.exists(base_config_path):
            raise FileNotFoundError(f"Base config file not found: {base_config_path}")
    
    def suggest_hyperparameters(self, trial: Trial) -> Dict[str, Any]:
        """
        建议超参数值
        
        Args:
            trial: optuna trial对象
            
        Returns:
            超参数字典
        """
        hyperparams = {}
        
        # 高优先级超参数
        # 学习率策略
        hyperparams['base_lr'] = trial.suggest_float('base_lr', 1e-5, 1e-2, log=True)
        hyperparams['image_backbone_lr_mult'] = trial.suggest_float('image_backbone_lr_mult', 0.05, 0.2)
        hyperparams['cls_head_lr_mult'] = trial.suggest_float('cls_head_lr_mult', 0.8, 2.0)
        
        # 正则化参数
        hyperparams['fusion_dropout'] = trial.suggest_float('fusion_dropout', 0.3, 0.7)
        hyperparams['weight_decay'] = trial.suggest_float('weight_decay', 1e-5, 1e-3, log=True)
        
        # 训练策略
        hyperparams['batch_size'] = trial.suggest_categorical('batch_size', [2, 4, 6, 8])
        
        # 中优先级超参数
        hyperparams['fusion_dim'] = trial.suggest_categorical('fusion_dim', [256, 384, 512, 768, 1024])
        hyperparams['warmup_epochs'] = trial.suggest_int('warmup_epochs', 3, 10)
        
        return hyperparams
    
    def create_config(self, hyperparams: Dict[str, Any], trial_id: int) -> str:
        """
        基于超参数创建配置文件
        
        Args:
            hyperparams: 超参数字典
            trial_id: 试验ID
            
        Returns:
            生成的配置文件路径
        """
        # 读取基础配置文件
        with open(self.base_config_path, 'r', encoding='utf-8') as f:
            base_config = f.read()
        
        # 创建试验特定的配置
        trial_config = base_config
        
        # 替换超参数
        # 优化器配置
        optim_config = f"""
optim_wrapper = dict(
    type='OptimWrapper',
    optimizer=dict(
        type='AdamW',
        lr={hyperparams['base_lr']},
        betas=(0.9, 0.999),
        weight_decay={hyperparams['weight_decay']}
    ),
    paramwise_cfg=dict(
        custom_keys={{
            'image_backbone': dict(lr_mult={hyperparams['image_backbone_lr_mult']}),
            'pose_backbone': dict(lr_mult=1.0),
            'fusion_neck': dict(lr_mult=1.0),
            'cls_head': dict(lr_mult={hyperparams['cls_head_lr_mult']})
        }}
    ),
    clip_grad=dict(max_norm=20, norm_type=2)
)
"""
        
        # 模型配置更新
        model_config = f"""
# 更新模型配置
model.update(dict(
    fusion_neck=dict(
        type='MultiModalFusionNeck',
        pose_feat_dim=256,
        img_feat_dim=320,
        fusion_dim={hyperparams['fusion_dim']},
        fusion_type='attention',
        dropout={hyperparams['fusion_dropout']}
    )
))
"""
        
        # 数据加载器配置
        dataloader_config = f"""
# 更新数据加载器配置
train_dataloader.update(dict(batch_size={hyperparams['batch_size']}))
val_dataloader.update(dict(batch_size={hyperparams['batch_size']}))
"""
        
        # 学习率调度器配置
        scheduler_config = f"""
# 学习率调度器配置
param_scheduler = [
    dict(
        type='LinearLR',
        start_factor=0.01,
        by_epoch=True,
        begin=0,
        end={hyperparams['warmup_epochs']}
    ),
    dict(
        type='CosineAnnealingLR',
        T_max={100 - hyperparams['warmup_epochs']},
        eta_min=1e-6,
        by_epoch=True,
        begin={hyperparams['warmup_epochs']},
        end=100
    )
]
"""
        
        # 工作目录配置
        work_dir = os.path.join(self.work_dir_base, f"trial_{trial_id}")
        work_dir_config = f"work_dir = '{work_dir}'"

        # Hook配置（集成OptunaHook）
        hook_config = f"""
# Optuna Hook Configuration
custom_hooks = [
    dict(
        type='OptunaHook',
        monitor_metric='val/acc',
        pruning_enabled=True,
        min_epochs=10,
        patience=5
    )
]
"""

        # 组合完整配置
        trial_config += f"\n\n# Optuna Trial {trial_id} Configuration\n"
        trial_config += optim_config + "\n"
        trial_config += model_config + "\n"
        trial_config += dataloader_config + "\n"
        trial_config += scheduler_config + "\n"
        trial_config += work_dir_config + "\n"
        trial_config += hook_config + "\n"
        
        # 保存配置文件
        config_path = os.path.join(self.work_dir_base, f"trial_{trial_id}_config.py")
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(trial_config)
        
        logger.info(f"Created config file: {config_path}")
        return config_path
    
    def run_training(self, config_path: str) -> Dict[str, Any]:
        """
        执行训练
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            训练结果字典
        """
        # 构建训练命令
        cmd = [
            sys.executable, "tools/train_m2.py",
            "--config", config_path,
            "--no-validate"  # 暂时禁用验证以加快训练速度
        ]
        
        logger.info(f"Running training command: {' '.join(cmd)}")
        
        try:
            # 执行训练
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=3600,  # 1小时超时
                cwd=os.getcwd()
            )
            
            if result.returncode != 0:
                logger.error(f"Training failed with return code {result.returncode}")
                logger.error(f"Error output: {result.stderr}")
                raise RuntimeError(f"Training failed: {result.stderr}")
            
            logger.info("Training completed successfully")
            return {"status": "success", "stdout": result.stdout, "stderr": result.stderr}
            
        except subprocess.TimeoutExpired:
            logger.error("Training timeout")
            raise RuntimeError("Training timeout")
        except Exception as e:
            logger.error(f"Training error: {str(e)}")
            raise
    
    def extract_metrics(self, work_dir: str) -> float:
        """
        从训练结果中提取评估指标

        Args:
            work_dir: 工作目录路径

        Returns:
            优化目标值（越小越好，这里返回负的准确率）
        """
        # 查找最新的日志文件
        log_files = list(Path(work_dir).glob("*.log"))
        if not log_files:
            logger.warning(f"No log files found in {work_dir}")
            return float('inf')  # 返回最差值

        latest_log = max(log_files, key=os.path.getctime)

        try:
            with open(latest_log, 'r', encoding='utf-8') as f:
                log_content = f.read()

            # 改进的指标提取逻辑
            best_acc = self._extract_best_accuracy(log_content)
            if best_acc is not None:
                # 返回负的准确率，因为optuna要最小化目标
                logger.info(f"Extracted best accuracy: {best_acc:.4f}")
                return -best_acc

            # 如果找不到准确率，尝试提取损失值
            best_loss = self._extract_best_loss(log_content)
            if best_loss is not None:
                logger.info(f"Extracted best loss: {best_loss:.4f}")
                return best_loss

            logger.warning("Could not extract any meaningful metrics")
            return float('inf')

        except Exception as e:
            logger.error(f"Error extracting metrics: {str(e)}")
            return float('inf')

    def _extract_best_accuracy(self, log_content: str) -> Optional[float]:
        """从日志中提取最佳准确率"""
        import re

        # 寻找准确率相关的模式
        acc_patterns = [
            r'acc[^:]*:\s*([0-9.]+)',
            r'accuracy[^:]*:\s*([0-9.]+)',
            r'val/acc[^:]*:\s*([0-9.]+)',
            r'val_acc[^:]*:\s*([0-9.]+)'
        ]

        best_acc = None
        for pattern in acc_patterns:
            matches = re.findall(pattern, log_content, re.IGNORECASE)
            if matches:
                accuracies = [float(match) for match in matches]
                current_best = max(accuracies)
                if best_acc is None or current_best > best_acc:
                    best_acc = current_best

        return best_acc

    def _extract_best_loss(self, log_content: str) -> Optional[float]:
        """从日志中提取最佳损失值"""
        import re

        # 寻找损失相关的模式
        loss_patterns = [
            r'loss[^:]*:\s*([0-9.]+)',
            r'val/loss[^:]*:\s*([0-9.]+)',
            r'val_loss[^:]*:\s*([0-9.]+)'
        ]

        best_loss = None
        for pattern in loss_patterns:
            matches = re.findall(pattern, log_content, re.IGNORECASE)
            if matches:
                losses = [float(match) for match in matches]
                current_best = min(losses)  # 损失越小越好
                if best_loss is None or current_best < best_loss:
                    best_loss = current_best

        return best_loss
    
    def objective(self, trial: Trial) -> float:
        """
        Optuna目标函数
        
        Args:
            trial: optuna trial对象
            
        Returns:
            优化目标值
        """
        logger.info(f"Starting trial {trial.number}")
        
        try:
            # 1. 建议超参数
            hyperparams = self.suggest_hyperparameters(trial)
            logger.info(f"Trial {trial.number} hyperparameters: {hyperparams}")
            
            # 2. 创建配置文件
            config_path = self.create_config(hyperparams, trial.number)
            
            # 3. 执行训练
            training_result = self.run_training(config_path)
            
            # 4. 提取指标
            work_dir = os.path.join(self.work_dir_base, f"trial_{trial.number}")
            objective_value = self.extract_metrics(work_dir)
            
            logger.info(f"Trial {trial.number} completed with objective value: {objective_value}")
            return objective_value
            
        except Exception as e:
            logger.error(f"Trial {trial.number} failed: {str(e)}")
            return float('inf')  # 返回最差值


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Optuna超参数优化训练')
    parser.add_argument('--config', 
                       default='configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py',
                       help='基础配置文件路径')
    parser.add_argument('--study-name', default='mmaction2_hyperopt', help='Study名称')
    parser.add_argument('--n-trials', type=int, default=20, help='试验次数')
    parser.add_argument('--storage', help='Optuna存储URL')
    
    args = parser.parse_args()
    
    # 创建训练管理器
    trainer = OptunaTrainer(
        base_config_path=args.config,
        study_name=args.study_name,
        storage_url=args.storage
    )
    
    # 创建或加载study
    study = optuna.create_study(
        study_name=args.study_name,
        storage=args.storage,
        direction='minimize',  # 最小化损失
        load_if_exists=True
    )
    
    logger.info(f"Starting optimization with {args.n_trials} trials")
    
    # 开始优化
    study.optimize(trainer.objective, n_trials=args.n_trials)
    
    # 输出最佳结果
    logger.info("Optimization completed!")
    logger.info(f"Best trial: {study.best_trial.number}")
    logger.info(f"Best value: {study.best_value}")
    logger.info(f"Best params: {study.best_params}")


if __name__ == '__main__':
    main()
