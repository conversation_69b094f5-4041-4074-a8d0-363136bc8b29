#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Optuna可视化Dashboard工具
Created by: Moss
Date: 2025-08-23
"""

import os
import sys
import argparse
import logging
import subprocess
import sqlite3
from pathlib import Path
import optuna
from optuna_dashboard import run_server

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OptunaDashboard:
    """
    Optuna可视化Dashboard管理器
    
    功能：
    1. 启动optuna-dashboard服务
    2. 管理study存储
    3. 提供可视化界面
    """
    
    def __init__(self, 
                 storage_url: str = "sqlite:///optuna_studies.db",
                 host: str = "localhost",
                 port: int = 8080):
        """
        初始化Dashboard
        
        Args:
            storage_url: 存储URL
            host: 服务器主机
            port: 服务器端口
        """
        self.storage_url = storage_url
        self.host = host
        self.port = port
        
        # 确保存储目录存在
        if storage_url.startswith("sqlite:///"):
            db_path = storage_url.replace("sqlite:///", "")
            os.makedirs(os.path.dirname(os.path.abspath(db_path)), exist_ok=True)
    
    def start_dashboard(self):
        """启动Dashboard服务"""
        logger.info(f"🚀 启动Optuna Dashboard")
        logger.info(f"📊 存储: {self.storage_url}")
        logger.info(f"🌐 地址: http://{self.host}:{self.port}")
        
        try:
            run_server(
                storage=self.storage_url,
                host=self.host,
                port=self.port,
                debug=False
            )
        except KeyboardInterrupt:
            logger.info("👋 Dashboard已停止")
        except Exception as e:
            logger.error(f"❌ Dashboard启动失败: {str(e)}")
    
    def create_study(self, study_name: str, direction: str = 'minimize') -> optuna.Study:
        """
        创建或加载study
        
        Args:
            study_name: study名称
            direction: 优化方向
            
        Returns:
            optuna Study对象
        """
        try:
            study = optuna.create_study(
                study_name=study_name,
                storage=self.storage_url,
                direction=direction,
                load_if_exists=True
            )
            logger.info(f"✅ Study创建/加载成功: {study_name}")
            return study
        except Exception as e:
            logger.error(f"❌ Study创建失败: {str(e)}")
            raise
    
    def list_studies(self):
        """列出所有studies"""
        try:
            studies = optuna.get_all_study_summaries(storage=self.storage_url)
            
            if not studies:
                logger.info("📋 没有找到任何studies")
                return
            
            logger.info("📋 所有Studies:")
            for study in studies:
                logger.info(f"  - {study.study_name}: {len(study.n_trials)} 试验")
                
        except Exception as e:
            logger.error(f"❌ 获取studies列表失败: {str(e)}")
    
    def get_study_info(self, study_name: str):
        """获取study详细信息"""
        try:
            study = optuna.load_study(
                study_name=study_name,
                storage=self.storage_url
            )
            
            logger.info(f"📊 Study信息: {study_name}")
            logger.info(f"  试验数量: {len(study.trials)}")
            logger.info(f"  优化方向: {study.direction}")
            
            if study.best_trial:
                logger.info(f"  最佳试验: {study.best_trial.number}")
                logger.info(f"  最佳值: {study.best_value:.4f}")
                logger.info(f"  最佳参数: {study.best_params}")
            
            # 显示最近的几个试验
            recent_trials = study.trials[-5:] if len(study.trials) > 5 else study.trials
            logger.info("  最近试验:")
            for trial in recent_trials:
                status = "✅" if trial.state == optuna.trial.TrialState.COMPLETE else "❌"
                value_str = f"{trial.value:.4f}" if trial.value is not None else "N/A"
                logger.info(f"    试验 {trial.number}: {status} 值={value_str}")
                
        except Exception as e:
            logger.error(f"❌ 获取study信息失败: {str(e)}")
    
    def export_study(self, study_name: str, output_file: str):
        """导出study数据"""
        try:
            study = optuna.load_study(
                study_name=study_name,
                storage=self.storage_url
            )
            
            # 导出为CSV格式
            import pandas as pd
            
            data = []
            for trial in study.trials:
                row = {
                    'trial_number': trial.number,
                    'value': trial.value,
                    'state': trial.state.name,
                    'datetime_start': trial.datetime_start,
                    'datetime_complete': trial.datetime_complete
                }
                # 添加参数
                row.update(trial.params)
                data.append(row)
            
            df = pd.DataFrame(data)
            df.to_csv(output_file, index=False)
            
            logger.info(f"✅ Study数据已导出到: {output_file}")
            
        except Exception as e:
            logger.error(f"❌ 导出study失败: {str(e)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Optuna Dashboard工具')
    parser.add_argument('--action', 
                       choices=['start', 'list', 'info', 'export'],
                       default='start',
                       help='操作类型')
    parser.add_argument('--storage', 
                       default='sqlite:///work_dirs/optuna_studies.db',
                       help='存储URL')
    parser.add_argument('--host', default='localhost', help='服务器主机')
    parser.add_argument('--port', type=int, default=8080, help='服务器端口')
    parser.add_argument('--study-name', help='Study名称')
    parser.add_argument('--output', help='导出文件路径')
    
    args = parser.parse_args()
    
    # 创建Dashboard管理器
    dashboard = OptunaDashboard(
        storage_url=args.storage,
        host=args.host,
        port=args.port
    )
    
    logger.info("=" * 60)
    logger.info("Optuna Dashboard工具")
    logger.info("=" * 60)
    
    if args.action == 'start':
        logger.info("🚀 启动Dashboard服务")
        dashboard.start_dashboard()
        
    elif args.action == 'list':
        logger.info("📋 列出所有Studies")
        dashboard.list_studies()
        
    elif args.action == 'info':
        if not args.study_name:
            logger.error("❌ 请指定study名称: --study-name")
            return
        logger.info(f"📊 获取Study信息: {args.study_name}")
        dashboard.get_study_info(args.study_name)
        
    elif args.action == 'export':
        if not args.study_name or not args.output:
            logger.error("❌ 请指定study名称和输出文件: --study-name --output")
            return
        logger.info(f"📤 导出Study数据: {args.study_name}")
        dashboard.export_study(args.study_name, args.output)
    
    logger.info("=" * 60)


if __name__ == '__main__':
    main()
