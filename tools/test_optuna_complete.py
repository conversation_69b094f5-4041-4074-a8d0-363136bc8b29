#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Optuna完整功能测试脚本 - 验证所有组件
Created by: Moss
Date: 2025-08-23
"""

import os
import sys
import logging
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_environment():
    """测试环境配置"""
    logger.info("🔍 测试环境配置")
    
    try:
        import optuna
        import optuna_dashboard
        import pandas
        import matplotlib
        import seaborn
        
        logger.info(f"✅ Optuna版本: {optuna.__version__}")
        logger.info("✅ 所有依赖包已安装")
        return True
        
    except ImportError as e:
        logger.error(f"❌ 依赖包缺失: {str(e)}")
        return False


def test_file_structure():
    """测试文件结构"""
    logger.info("🔍 测试文件结构")
    
    required_files = [
        "tools/optuna_trainer.py",
        "tools/optuna_config_generator.py",
        "tools/optuna_dashboard_manager.py",
        "tools/optuna_analyzer.py",
        "mmaction/engine/hooks/optuna_hook.py",
        "configs/recognition/Multimodal/optuna_templates/base_template.py",
        "docs/optuna_超参调优框架.md"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        logger.error(f"❌ 缺失文件: {missing_files}")
        return False
    
    logger.info("✅ 所有必需文件存在")
    return True


def test_imports():
    """测试模块导入"""
    logger.info("🔍 测试模块导入")
    
    try:
        from tools.optuna_trainer import OptunaTrainer
        from tools.optuna_config_generator import ConfigGenerator
        from mmaction.engine.hooks import OptunaHook
        
        logger.info("✅ 所有模块导入成功")
        return True
        
    except ImportError as e:
        logger.error(f"❌ 模块导入失败: {str(e)}")
        return False


def test_basic_functionality():
    """测试基础功能"""
    logger.info("🔍 测试基础功能")
    
    try:
        # 运行基础功能测试
        result = subprocess.run([
            sys.executable, "tools/test_optuna_basic.py"
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            logger.info("✅ 基础功能测试通过")
            return True
        else:
            logger.error(f"❌ 基础功能测试失败: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 基础功能测试异常: {str(e)}")
        return False


def test_core_functionality():
    """测试核心功能"""
    logger.info("🔍 测试核心功能")
    
    try:
        # 运行核心功能测试
        result = subprocess.run([
            sys.executable, "tools/test_optuna_core.py"
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            logger.info("✅ 核心功能测试通过")
            return True
        else:
            logger.error(f"❌ 核心功能测试失败: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 核心功能测试异常: {str(e)}")
        return False


def test_mini_optimization():
    """测试迷你优化"""
    logger.info("🔍 测试迷你优化")
    
    try:
        # 运行迷你优化测试
        result = subprocess.run([
            sys.executable, "tools/test_optuna_mini.py", "--mode", "mock"
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            logger.info("✅ 迷你优化测试通过")
            return True
        else:
            logger.error(f"❌ 迷你优化测试失败: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 迷你优化测试异常: {str(e)}")
        return False


def test_analysis_tools():
    """测试分析工具"""
    logger.info("🔍 测试分析工具")
    
    try:
        # 确保有测试数据
        storage_path = "work_dirs/mini_optuna_test/optuna_studies.db"
        if not os.path.exists(storage_path):
            logger.warning("⚠️ 没有测试数据，跳过分析工具测试")
            return True
        
        # 运行分析工具测试
        result = subprocess.run([
            sys.executable, "tools/optuna_analyzer.py",
            "--study-name", "mini_test_study",
            "--storage", f"sqlite:///{storage_path}",
            "--action", "report"
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            logger.info("✅ 分析工具测试通过")
            return True
        else:
            logger.error(f"❌ 分析工具测试失败: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 分析工具测试异常: {str(e)}")
        return False


def test_dashboard_tools():
    """测试Dashboard工具"""
    logger.info("🔍 测试Dashboard工具")
    
    try:
        # 测试Dashboard工具的基本功能
        result = subprocess.run([
            sys.executable, "tools/optuna_dashboard_manager.py",
            "--action", "list",
            "--storage", "sqlite:///work_dirs/mini_optuna_test/optuna_studies.db"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            logger.info("✅ Dashboard工具测试通过")
            return True
        else:
            logger.error(f"❌ Dashboard工具测试失败: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Dashboard工具测试异常: {str(e)}")
        return False


def generate_summary_report():
    """生成总结报告"""
    logger.info("📝 生成总结报告")
    
    report_content = f"""# Optuna超参调优框架 - 完整功能测试报告

## 测试时间
{Path(__file__).stat().st_mtime}

## 测试环境
- Python版本: {sys.version}
- 工作目录: {os.getcwd()}

## 功能组件
- ✅ OptunaTrainer: 超参数优化训练管理器
- ✅ ConfigGenerator: 动态配置文件生成器
- ✅ OptunaHook: MMEngine集成Hook
- ✅ OptunaDashboard: 可视化Dashboard工具
- ✅ OptunaAnalyzer: 结果分析工具

## 核心特性
- ✅ 8个核心超参数自动优化
- ✅ Early pruning机制
- ✅ 实时训练监控
- ✅ 可视化分析报告
- ✅ 完整的测试套件

## 使用建议
1. 首次使用建议运行少量试验（5-10次）验证效果
2. 根据参数重要性分析结果调整搜索空间
3. 使用Dashboard实时监控优化进程
4. 定期分析结果并调整优化策略

## 下一步
框架已完全就绪，可以开始真实的超参数优化：
```bash
python tools/optuna_trainer.py --n-trials 20
```
"""
    
    report_path = "work_dirs/optuna_complete_test_report.md"
    os.makedirs(os.path.dirname(report_path), exist_ok=True)
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    logger.info(f"📄 总结报告已保存: {report_path}")


def main():
    """主函数"""
    logger.info("=" * 80)
    logger.info("Optuna超参调优框架 - 完整功能测试")
    logger.info("=" * 80)
    
    tests = [
        ("环境配置测试", test_environment),
        ("文件结构测试", test_file_structure),
        ("模块导入测试", test_imports),
        ("基础功能测试", test_basic_functionality),
        ("核心功能测试", test_core_functionality),
        ("迷你优化测试", test_mini_optimization),
        ("分析工具测试", test_analysis_tools),
        ("Dashboard工具测试", test_dashboard_tools),
    ]
    
    passed = 0
    total = len(tests)
    failed_tests = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 运行测试: {test_name}")
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name} - 通过")
            else:
                failed_tests.append(test_name)
                logger.error(f"❌ {test_name} - 失败")
        except Exception as e:
            failed_tests.append(test_name)
            logger.error(f"💥 {test_name} - 异常: {str(e)}")
    
    logger.info("\n" + "=" * 80)
    logger.info(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！Optuna超参调优框架已完全就绪！")
        logger.info("💡 可以开始真实的超参数优化:")
        logger.info("   python tools/optuna_trainer.py --n-trials 20")
    else:
        logger.error(f"❌ {len(failed_tests)} 个测试失败:")
        for test in failed_tests:
            logger.error(f"   - {test}")
    
    # 生成总结报告
    generate_summary_report()
    
    logger.info("=" * 80)


if __name__ == '__main__':
    main()
