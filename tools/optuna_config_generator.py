#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Optuna配置生成器 - 动态生成mmaction2配置文件
Created by: Moss
Date: 2025-08-23
"""

import os
import json
import tempfile
from typing import Dict, Any, List, Optional
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class ConfigGenerator:
    """
    配置文件生成器
    
    功能：
    1. 基于模板和超参数值生成完整配置文件
    2. 确保配置文件的正确性和一致性
    3. 支持不同优化策略的配置模板
    """
    
    def __init__(self, template_path: str):
        """
        初始化配置生成器
        
        Args:
            template_path: 配置模板文件路径
        """
        self.template_path = template_path
        
        if not os.path.exists(template_path):
            raise FileNotFoundError(f"Template file not found: {template_path}")
    
    def generate_config(self, hyperparams: Dict[str, Any], output_path: str) -> str:
        """
        生成配置文件
        
        Args:
            hyperparams: 超参数字典
            output_path: 输出配置文件路径
            
        Returns:
            生成的配置文件路径
        """
        # 读取模板文件
        with open(self.template_path, 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        # 生成配置内容
        config_content = self._generate_config_content(template_content, hyperparams)
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 写入配置文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        logger.info(f"Generated config file: {output_path}")
        return output_path
    
    def _generate_config_content(self, template: str, hyperparams: Dict[str, Any]) -> str:
        """
        基于模板和超参数生成配置内容
        
        Args:
            template: 模板内容
            hyperparams: 超参数字典
            
        Returns:
            生成的配置内容
        """
        config_content = template
        
        # 添加超参数配置
        config_content += "\n\n# Optuna Hyperparameter Configuration\n"
        
        # 优化器配置
        optim_config = self._generate_optimizer_config(hyperparams)
        config_content += optim_config + "\n"
        
        # 模型配置
        model_config = self._generate_model_config(hyperparams)
        config_content += model_config + "\n"
        
        # 数据加载器配置
        dataloader_config = self._generate_dataloader_config(hyperparams)
        config_content += dataloader_config + "\n"
        
        # 学习率调度器配置
        scheduler_config = self._generate_scheduler_config(hyperparams)
        config_content += scheduler_config + "\n"
        
        return config_content
    
    def _generate_optimizer_config(self, hyperparams: Dict[str, Any]) -> str:
        """生成优化器配置"""
        return f"""
# Optimizer Configuration
optim_wrapper = dict(
    type='OptimWrapper',
    optimizer=dict(
        type='AdamW',
        lr={hyperparams.get('base_lr', 0.001)},
        betas=(0.9, 0.999),
        weight_decay={hyperparams.get('weight_decay', 0.0001)}
    ),
    paramwise_cfg=dict(
        custom_keys={{
            'image_backbone': dict(lr_mult={hyperparams.get('image_backbone_lr_mult', 0.1)}),
            'pose_backbone': dict(lr_mult=1.0),
            'fusion_neck': dict(lr_mult={hyperparams.get('fusion_neck_lr_mult', 1.0)}),
            'cls_head': dict(lr_mult={hyperparams.get('cls_head_lr_mult', 1.0)})
        }}
    ),
    clip_grad=dict(max_norm=20, norm_type=2)
)"""
    
    def _generate_model_config(self, hyperparams: Dict[str, Any]) -> str:
        """生成模型配置"""
        return f"""
# Model Configuration Updates
model.update(dict(
    fusion_neck=dict(
        type='MultiModalFusionNeck',
        pose_feat_dim=256,
        img_feat_dim=320,
        fusion_dim={hyperparams.get('fusion_dim', 512)},
        fusion_type='attention',
        dropout={hyperparams.get('fusion_dropout', 0.5)}
    )
))"""
    
    def _generate_dataloader_config(self, hyperparams: Dict[str, Any]) -> str:
        """生成数据加载器配置"""
        batch_size = hyperparams.get('batch_size', 4)
        return f"""
# DataLoader Configuration Updates
train_dataloader.update(dict(batch_size={batch_size}))
val_dataloader.update(dict(batch_size={batch_size}))"""
    
    def _generate_scheduler_config(self, hyperparams: Dict[str, Any]) -> str:
        """生成学习率调度器配置"""
        warmup_epochs = hyperparams.get('warmup_epochs', 5)
        max_epochs = hyperparams.get('max_epochs', 100)
        
        return f"""
# Learning Rate Scheduler Configuration
param_scheduler = [
    dict(
        type='LinearLR',
        start_factor=0.01,
        by_epoch=True,
        begin=0,
        end={warmup_epochs}
    ),
    dict(
        type='CosineAnnealingLR',
        T_max={max_epochs - warmup_epochs},
        eta_min=1e-6,
        by_epoch=True,
        begin={warmup_epochs},
        end={max_epochs}
    )
]"""
    
    def validate_config(self, config_path: str) -> bool:
        """
        验证配置文件的正确性
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            是否验证通过
        """
        try:
            # 尝试导入配置文件
            import importlib.util
            spec = importlib.util.spec_from_file_location("config", config_path)
            config_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(config_module)
            
            # 检查必要的配置项
            required_attrs = ['model', 'train_dataloader', 'val_dataloader', 'optim_wrapper']
            for attr in required_attrs:
                if not hasattr(config_module, attr):
                    logger.error(f"Missing required config attribute: {attr}")
                    return False
            
            logger.info(f"Config validation passed: {config_path}")
            return True
            
        except Exception as e:
            logger.error(f"Config validation failed: {str(e)}")
            return False
    
    @staticmethod
    def get_hyperparameter_spaces() -> Dict[str, Dict[str, Any]]:
        """
        获取超参数搜索空间定义
        
        Returns:
            超参数空间字典
        """
        return {
            # 高优先级超参数
            'base_lr': {
                'type': 'float',
                'low': 1e-5,
                'high': 1e-2,
                'log': True,
                'description': '基础学习率'
            },
            'image_backbone_lr_mult': {
                'type': 'float',
                'low': 0.05,
                'high': 0.2,
                'description': '图像骨干网络学习率倍数'
            },
            'fusion_neck_lr_mult': {
                'type': 'float',
                'low': 0.5,
                'high': 2.0,
                'description': '融合模块学习率倍数'
            },
            'cls_head_lr_mult': {
                'type': 'float',
                'low': 0.8,
                'high': 2.0,
                'description': '分类头学习率倍数'
            },
            'fusion_dropout': {
                'type': 'float',
                'low': 0.3,
                'high': 0.7,
                'description': '融合模块dropout率'
            },
            'weight_decay': {
                'type': 'float',
                'low': 1e-5,
                'high': 1e-3,
                'log': True,
                'description': '权重衰减'
            },
            'batch_size': {
                'type': 'categorical',
                'choices': [2, 4, 6, 8],
                'description': '批次大小'
            },
            
            # 中优先级超参数
            'fusion_dim': {
                'type': 'categorical',
                'choices': [256, 384, 512, 768, 1024],
                'description': '融合特征维度'
            },
            'warmup_epochs': {
                'type': 'int',
                'low': 3,
                'high': 10,
                'description': '预热轮数'
            }
        }


def create_base_template():
    """创建基础配置模板"""
    template_content = '''# Optuna Base Configuration Template
# This template will be extended with hyperparameter-specific configurations

# Import base configuration
_base_ = '../multimodal_poseGCN-rgbR50_fusion.py'

# Base settings that will be overridden by hyperparameter configurations
# The actual hyperparameter values will be appended to this template
'''
    
    template_path = "configs/recognition/Multimodal/optuna_templates/base_template.py"
    os.makedirs(os.path.dirname(template_path), exist_ok=True)
    
    with open(template_path, 'w', encoding='utf-8') as f:
        f.write(template_content)
    
    logger.info(f"Created base template: {template_path}")
    return template_path


def main():
    """测试配置生成器"""
    # 创建基础模板
    template_path = create_base_template()
    
    # 创建配置生成器
    generator = ConfigGenerator(template_path)
    
    # 测试超参数
    test_hyperparams = {
        'base_lr': 0.001,
        'image_backbone_lr_mult': 0.1,
        'cls_head_lr_mult': 1.2,
        'fusion_dropout': 0.5,
        'weight_decay': 0.0001,
        'batch_size': 4,
        'fusion_dim': 512,
        'warmup_epochs': 5
    }
    
    # 生成测试配置
    output_path = "configs/recognition/Multimodal/optuna_templates/test_config.py"
    generator.generate_config(test_hyperparams, output_path)
    
    # 验证配置
    is_valid = generator.validate_config(output_path)
    print(f"Config validation result: {is_valid}")
    
    # 显示超参数空间
    spaces = ConfigGenerator.get_hyperparameter_spaces()
    print("Hyperparameter spaces:")
    for name, space in spaces.items():
        print(f"  {name}: {space['description']}")


if __name__ == '__main__':
    main()
