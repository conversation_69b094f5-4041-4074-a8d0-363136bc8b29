#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Optuna迷你优化测试 - 验证完整流程
Created by: Moss
Date: 2025-08-23
"""

import os
import sys
import logging
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.optuna_trainer import OptunaTrainer
import optuna

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MockOptunaTrainer(OptunaTrainer):
    """
    模拟OptunaTrainer，用于快速测试
    不实际运行训练，而是模拟训练结果
    """
    
    def run_training(self, config_path: str) -> dict:
        """
        模拟训练过程
        """
        logger.info(f"🔄 模拟训练: {config_path}")
        
        # 模拟训练时间
        import time
        time.sleep(2)  # 模拟2秒训练时间
        
        # 模拟训练成功
        return {"status": "success", "stdout": "Mock training completed", "stderr": ""}
    
    def extract_metrics(self, work_dir: str) -> float:
        """
        模拟指标提取
        返回随机的目标值
        """
        import random
        
        # 模拟准确率在0.7-0.9之间
        mock_accuracy = random.uniform(0.7, 0.9)
        
        # 创建模拟的工作目录和日志文件
        os.makedirs(work_dir, exist_ok=True)
        log_file = os.path.join(work_dir, "mock_training.log")
        
        with open(log_file, 'w') as f:
            f.write(f"Mock training log\n")
            f.write(f"Epoch 1: loss: 1.234, acc: {mock_accuracy-0.1:.3f}\n")
            f.write(f"Epoch 2: loss: 1.123, acc: {mock_accuracy-0.05:.3f}\n")
            f.write(f"Epoch 3: loss: 1.012, val/acc: {mock_accuracy:.3f}\n")
        
        logger.info(f"📊 模拟指标: accuracy = {mock_accuracy:.3f}")
        
        # 返回负的准确率（因为optuna要最小化）
        return -mock_accuracy


def test_mini_optimization():
    """运行迷你优化测试"""
    logger.info("🚀 开始迷你优化测试")
    
    # 创建模拟训练器
    base_config = "configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py"
    
    if not os.path.exists(base_config):
        logger.error(f"❌ 基础配置文件不存在: {base_config}")
        return False
    
    trainer = MockOptunaTrainer(
        base_config_path=base_config,
        study_name="mini_test_study",
        work_dir_base="./work_dirs/mini_optuna_test"
    )
    
    # 创建study（使用持久化存储）
    storage_url = "sqlite:///work_dirs/mini_optuna_test/optuna_studies.db"
    os.makedirs("work_dirs/mini_optuna_test", exist_ok=True)

    study = optuna.create_study(
        study_name="mini_test_study",
        storage=storage_url,
        direction='minimize',  # 最小化目标（负的准确率）
        load_if_exists=True
    )
    
    logger.info("📈 开始优化（3次试验）")
    
    try:
        # 运行3次试验
        study.optimize(trainer.objective, n_trials=3)
        
        # 输出结果
        logger.info("🎉 优化完成！")
        logger.info(f"最佳试验: {study.best_trial.number}")
        logger.info(f"最佳值: {study.best_value:.4f}")
        logger.info(f"最佳参数: {study.best_params}")
        
        # 显示所有试验结果
        logger.info("\n📋 所有试验结果:")
        for trial in study.trials:
            status = "✅" if trial.state == optuna.trial.TrialState.COMPLETE else "❌"
            logger.info(f"  试验 {trial.number}: {status} 值={trial.value:.4f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 优化测试失败: {str(e)}")
        return False


def test_real_config_validation():
    """测试真实配置文件的有效性"""
    logger.info("🔍 测试真实配置文件验证")
    
    try:
        base_config = "configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py"
        trainer = OptunaTrainer(base_config_path=base_config)
        
        # 创建一个测试配置
        test_hyperparams = {
            'base_lr': 0.001,
            'image_backbone_lr_mult': 0.1,
            'cls_head_lr_mult': 1.2,
            'fusion_dropout': 0.5,
            'weight_decay': 0.0001,
            'batch_size': 4,
            'fusion_dim': 512,
            'warmup_epochs': 5
        }
        
        config_path = trainer.create_config(test_hyperparams, trial_id=9999)
        
        # 尝试验证配置文件
        logger.info(f"📄 生成的配置文件: {config_path}")
        
        # 简单的语法检查
        with open(config_path, 'r') as f:
            content = f.read()
        
        # 检查是否包含必要的配置
        required_sections = [
            'optim_wrapper',
            'model.update',
            'param_scheduler',
            'custom_hooks'
        ]
        
        missing_sections = []
        for section in required_sections:
            if section not in content:
                missing_sections.append(section)
        
        if missing_sections:
            logger.error(f"❌ 配置文件缺少必要部分: {missing_sections}")
            return False
        
        logger.info("✅ 配置文件验证通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置文件验证失败: {str(e)}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Optuna迷你优化测试')
    parser.add_argument('--mode', choices=['mock', 'validate'], default='mock',
                       help='测试模式: mock=模拟优化, validate=配置验证')
    
    args = parser.parse_args()
    
    logger.info("=" * 60)
    logger.info("Optuna迷你优化测试")
    logger.info("=" * 60)
    
    if args.mode == 'mock':
        logger.info("🎭 运行模式: 模拟优化测试")
        success = test_mini_optimization()
    else:
        logger.info("📋 运行模式: 配置验证测试")
        success = test_real_config_validation()
    
    logger.info("\n" + "=" * 60)
    if success:
        logger.info("🎉 测试成功完成！")
        if args.mode == 'mock':
            logger.info("💡 下一步可以尝试真实的optuna优化:")
            logger.info("   python tools/optuna_trainer.py --n-trials 5")
    else:
        logger.error("❌ 测试失败，请检查问题")
    
    logger.info("=" * 60)


if __name__ == '__main__':
    main()
